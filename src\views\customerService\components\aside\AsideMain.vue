<template>
    <el-scrollbar height="calc(100% - 60px)">
        <div :collapse="collapse" class="customer-service-users">
            <!-- 分类展示用户列表 -->
            <div v-for="(users, type) in groupedUsers" :key="type" class="user-category">
                <div class="category-header" @click="toggleCategory(type)">
                    <el-icon class="category-icon" :class="{ 'is-expanded': expandedCategories[type] }">
                        <CaretRight />
                    </el-icon>
                    <span class="category-title">{{ getCategoryTitle(type) }}</span>
                    <span class="category-count">({{ users.length }})</span>
                </div>

                <div v-show="expandedCategories[type]" class="category-content">
                    <div
                        v-for="messageUser in users"
                        :key="messageUser.chatWithUserInfo?.userId"
                        :index="messageUser.chatWithUserInfo?.userId"
                        class="user_msg_menu_item"
                        :class="{
                            'is-active': messageUser.chatWithUserInfo?.userId === currentSelectUser?.chatWithUserInfo?.userId,
                        }"
                        @click="onChange(messageUser)"
                    >
                        <div v-if="messageUser.chatWithUserInfo.includeRights" class="special_tag">
                            <QIcon svg name="icon-a-zuhe12851" style="width: 100%; height: 100%"></QIcon>
                        </div>
                        <el-badge :hidden="messageUser.lastMessage?.read" is-dot class="badge">
                            <el-avatar class="avatar" :size="45" shape="square" :src="getUserAvatar(messageUser.chatWithUserInfo.avatar)" />
                </el-badge>
                <div class="user-desc">
                    <div class="user-msg">
                        <div class="nickname">
                            {{ getUserNickname(messageUser.chatWithUserInfo?.userId, messageUser.chatWithUserInfo.nickname) }}
                        </div>
                        <div class="last-time">
                            {{ renderTime(messageUser.lastMessage?.sendTime) }}
                        </div>
                    </div>
                    <div class="last-message">
                        {{ renderMessage(messageUser.lastMessage) }}
                    </div>
                </div>
            </div>
        </div>
    </el-scrollbar>
</template>
<script setup lang="ts">
import { ref, watch, nextTick, computed, reactive } from 'vue'
import type { PropType } from 'vue'
import { CaretRight } from '@element-plus/icons-vue'
import DateUtil from '@/utils/date'
import { MessageType, MessageUser, ILastMessage } from '../../types'
import { getUserNickname, getUserAvatar } from '@/libs/userHelper'
import { currentSelectUser } from '@/views/customerService'
import { useRoute } from 'vue-router'

const props = defineProps({
    messageUsers: {
        type: Array as PropType<Array<MessageUser>>,
        default: () => [],
    },
})

const collapse = ref(false)
const route = useRoute()
const emits = defineEmits(['change'])

const onChange = (selectUser: MessageUser) => {
    if (selectUser.lastMessage) {
        selectUser.lastMessage.read = true
    }
    emits('change', { ...selectUser })
}

watch(
    () => props.messageUsers,
    async (val) => {
        if (!route.query.id) {
            return
        }
        if (val[0]?.chatWithUserInfo) {
            const selectUser = val.find((item) => item.chatWithUserInfo?.userId === route.query.id)
            if (selectUser) {
                await nextTick()
                onChange(selectUser)
            }
        }
    },
)

const renderTime = (time: any) => {
    if (!time) return ''
    const lastTime = new Date(+time)
    const dateUtil = new DateUtil(lastTime)
    const isToday = new Date().getDay() === lastTime.getDay()
    return isToday ? dateUtil.getH() + ':' + dateUtil.getMin() : dateUtil.getYMD()
}

const renderMessage = (message: ILastMessage) => {
    if (!message) return ''
    switch (message.messageType) {
        case MessageType.PRODUCT:
            return '[商品]'
        case MessageType.IMAGE:
            return '[图片]'
        case MessageType.ORDER:
            return '[订单]'
        default:
            return message.message
    }
}

// 分类展开状态
const expandedCategories = reactive<Record<string, boolean>>({
    CONSUMER: true,
    SHOP_ADMIN: true,
    PLATFORM_ADMIN: true,
    SUPPLIER: true,
    OTHER: true,
})

// 根据receiverType分组用户
const groupedUsers = computed(() => {
    const groups: Record<string, MessageUser[]> = {
        CONSUMER: [],
        SHOP_ADMIN: [],
        PLATFORM_ADMIN: [],
        SUPPLIER: [],
        OTHER: [],
    }

    props.messageUsers.forEach((user) => {
        const receiverType = user.lastMessage?.receiver?.receiverType || 'OTHER'

        // 根据receiverType分类
        if (receiverType === 'CONSUMER') {
            groups.CONSUMER.push(user)
        } else if (receiverType === 'SHOP_ADMIN') {
            groups.SHOP_ADMIN.push(user)
        } else if (receiverType === 'PLATFORM_ADMIN') {
            groups.PLATFORM_ADMIN.push(user)
        } else if (receiverType === 'SUPPLIER' || receiverType === 'SUPPLIER_CONSOLE') {
            groups.SUPPLIER.push(user)
        } else {
            groups.OTHER.push(user)
        }
    })

    // 过滤掉空的分组
    const filteredGroups: Record<string, MessageUser[]> = {}
    Object.keys(groups).forEach((key) => {
        if (groups[key].length > 0) {
            filteredGroups[key] = groups[key]
        }
    })

    return filteredGroups
})

// 获取分类标题
const getCategoryTitle = (type: string) => {
    const titles: Record<string, string> = {
        CONSUMER: '普通用户',
        SHOP_ADMIN: '店铺管理员',
        PLATFORM_ADMIN: '平台管理员',
        SUPPLIER: '供应商',
        OTHER: '其他',
    }
    return titles[type] || '未知类型'
}

// 切换分类展开状态
const toggleCategory = (type: string) => {
    expandedCategories[type] = !expandedCategories[type]
}
</script>
<style scoped lang="scss">
.customer-service-users {
    padding: 0;
    border-right: none;

    .user-category {
        margin-bottom: 8px;

        .category-header {
            display: flex;
            align-items: center;
            padding: 8px 14px;
            background-color: #f0f2f5;
            cursor: pointer;
            user-select: none;
            border-bottom: 1px solid #e4e7ed;

            &:hover {
                background-color: #e6e8eb;
            }

            .category-icon {
                margin-right: 6px;
                transition: transform 0.3s;

                &.is-expanded {
                    transform: rotate(90deg);
                }
            }

            .category-title {
                font-weight: 500;
                color: #303133;
                font-size: 14px;
            }

            .category-count {
                margin-left: 4px;
                color: #909399;
                font-size: 12px;
            }
        }

        .category-content {
            border-left: 2px solid #e4e7ed;

            .user_msg_menu_item {
                margin-left: 8px;
                border-left: none;
            }
        }
    }
    .user_msg_menu_item {
        cursor: pointer;
        font-size: 14px;
        color: #333333;
        height: 80px;
        user-select: none;
        margin: 0 !important;
        padding: 17px 14px 16px 14px !important;
        border-bottom: 2px solid $cust_service_border_color;
        overflow: hidden;
        display: flex;
        align-items: center;
        position: relative;
        background-color: #f7f8fa;
        padding: 1px;
        .special_tag {
            position: absolute;
            width: 26px;
            height: 26px;
            right: 0;
            top: 0;
        }
        .badge {
            height: 45px;
            margin-right: 10px;
        }
        .avatar {
            border-radius: 5px;
        }
        .user-desc {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            height: 100%;
            .user-msg {
                display: flex;
                align-items: center;
                .nickname {
                    font-weight: bold;
                    width: 136px;
                    // 超出省略
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                .last-time {
                    margin-left: auto;
                    color: rgb(153, 153, 153);
                    font-size: 12px;
                }
            }
            .last-message {
                width: 142px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
    .user_msg_menu_item.is-active {
        background-color: rgba(85, 92, 253, 0.1);
    }
}
</style>
